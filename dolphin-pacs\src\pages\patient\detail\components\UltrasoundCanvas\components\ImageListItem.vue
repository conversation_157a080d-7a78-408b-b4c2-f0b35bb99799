<script lang="ts" setup>
import ImageThumbnail from './ImageThumbnail.vue'
import ImageActions from './ImageActions.vue'

defineOptions({
  name: "ImageListItem"
})

interface ImageData {
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}

interface Props {
  image: ImageData
  isSelected: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  select: []
  delete: []
  toggleVisibility: []
  analyze: []
}>()
</script>

<template>
  <div
    class="list-item"
    :class="{ 'active': isSelected }"
    @click="emit('select')"
  >
    <ImageThumbnail
      :src="image.thumbnail"
      :alt="image.name"
      size="medium"
    />

    <ImageActions
      :image="image"
      @delete="emit('delete')"
      @toggle-visibility="emit('toggleVisibility')"
      @analyze="emit('analyze')"
    />
  </div>
</template>

<style lang="scss" scoped>
.list-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;

  &:hover {
    background: var(--el-color-primary-light-9);
  }

  &.active {
    background: var(--el-color-primary-light-8);
    border-color: var(--el-color-primary-light-5);
  }

  .image-thumbnail {
    margin-right: 8px;
  }

  // 操作按钮始终显示
  :deep(.image-actions) {
    opacity: 1;
  }
}
</style>
